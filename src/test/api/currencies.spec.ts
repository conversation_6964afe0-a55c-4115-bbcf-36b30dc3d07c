import { expect } from "chai";
import * as request from "supertest";
import { createApplication } from "../../skywind/server";
import { generateToken } from "../../skywind/token";
import { logging } from "@skywind-group/sw-utils";
import type { FastifyInstance } from "fastify";

logging.setUpOutput({
    type: "console",
    logLevel: "info"
});

describe("Currencies API", () => {
    let server: FastifyInstance["server"];
    let validToken: string;

    before(async () => {
        const app = createApplication();
        await app.ready();
        server = app.server;
        validToken = await generateToken({});
    });

    describe("GET /v1/currencies", () => {
        it("should return all supported currencies with valid token", async () => {
            const res = await request(server)
                .get("/v1/currencies")
                .query({ token: validToken })
                .send();

            expect(res.status).to.equal(200);
            expect(res.body).to.have.property("total");
            expect(res.body).to.have.property("currencies");
            expect(res.body.total).to.be.a("number");
            expect(res.body.currencies).to.be.an("array");
            expect(res.body.total).to.equal(res.body.currencies.length);
            expect(res.body.total).to.be.greaterThan(0);
        });

        it("should return 401 without token", async () => {
            const res = await request(server)
                .get("/v1/currencies")
                .send();

            expect(res.status).to.equal(400); // Fastify validation error for missing required field
        });

        it("should return 403 with invalid token", async () => {
            const res = await request(server)
                .get("/v1/currencies")
                .query({ token: "invalid-token" })
                .send();

            expect(res.status).to.equal(403);
        });

        it("should filter currencies by type", async () => {
            const baseRes = await request(server)
                .get("/v1/currencies")
                .query({ token: validToken, type: "base" })
                .send();

            expect(baseRes.status).to.equal(200);
            expect(baseRes.body.currencies).to.be.an("array");
            baseRes.body.currencies.forEach(currency => {
                expect(currency.type).to.equal("base");
                expect(currency.artificial).to.be.false;
            });

            const artificialRes = await request(server)
                .get("/v1/currencies")
                .query({ token: validToken, type: "artificial" })
                .send();

            expect(artificialRes.status).to.equal(200);
            artificialRes.body.currencies.forEach(currency => {
                expect(currency.type).to.equal("artificial");
                expect(currency.artificial).to.be.true;
                expect(currency).to.have.property("originCurrency");
            });
        });

        it("should return 400 for invalid type filter", async () => {
            const res = await request(server)
                .get("/v1/currencies")
                .query({ token: validToken, type: "invalid" })
                .send();

            expect(res.status).to.equal(400);
        });

        it("should support includeMetadata parameter", async () => {
            const fullRes = await request(server)
                .get("/v1/currencies")
                .query({ token: validToken, includeMetadata: true })
                .send();

            expect(fullRes.status).to.equal(200);
            expect(fullRes.body.currencies.length).to.be.greaterThan(0);

            const minimalRes = await request(server)
                .get("/v1/currencies")
                .query({ token: validToken, includeMetadata: false })
                .send();

            expect(minimalRes.status).to.equal(200);
            expect(minimalRes.body.currencies.length).to.be.greaterThan(0);
            
            // Minimal response should have fewer properties
            const minimalCurrency = minimalRes.body.currencies[0];
            expect(minimalCurrency).to.have.property("code");
            expect(minimalCurrency).to.have.property("type");
            expect(minimalCurrency).to.have.property("artificial");
            expect(Object.keys(minimalCurrency)).to.have.length(3);
        });

        it("should return currencies in sorted order", async () => {
            const res = await request(server)
                .get("/v1/currencies")
                .query({ token: validToken })
                .send();

            expect(res.status).to.equal(200);
            const codes = res.body.currencies.map(c => c.code);
            const sortedCodes = [...codes].sort();
            expect(codes).to.deep.equal(sortedCodes);
        });

        it("should have valid currency structure", async () => {
            const res = await request(server)
                .get("/v1/currencies")
                .query({ token: validToken })
                .send();

            expect(res.status).to.equal(200);
            res.body.currencies.forEach(currency => {
                expect(currency).to.have.property("code");
                expect(currency).to.have.property("type");
                expect(currency).to.have.property("artificial");
                expect(currency.code).to.be.a("string");
                expect(currency.type).to.be.oneOf(["base", "standard", "artificial"]);
                expect(currency.artificial).to.be.a("boolean");
            });
        });
    });

    describe("GET /v1/currencies/:code", () => {
        it("should return currency details for supported currency", async () => {
            const res = await request(server)
                .get("/v1/currencies/USD")
                .query({ token: validToken })
                .send();

            expect(res.status).to.equal(200);
            expect(res.body).to.have.property("supported", true);
            expect(res.body).to.have.property("currency");
            expect(res.body.currency).to.have.property("code", "USD");
            expect(res.body.currency).to.have.property("type");
            expect(res.body.currency).to.have.property("artificial");
        });

        it("should return 404 for unsupported currency", async () => {
            const res = await request(server)
                .get("/v1/currencies/ZZZ")
                .query({ token: validToken })
                .send();

            expect(res.status).to.equal(404);
            expect(res.body).to.have.property("supported", false);
            expect(res.body).to.have.property("message");
        });

        it("should handle lowercase currency codes", async () => {
            const res = await request(server)
                .get("/v1/currencies/usd")
                .query({ token: validToken })
                .send();

            expect(res.status).to.equal(200);
            expect(res.body.supported).to.be.true;
            expect(res.body.currency.code).to.equal("USD");
        });

        it("should return 400 for invalid currency code format", async () => {
            const res = await request(server)
                .get("/v1/currencies/INVALID123")
                .query({ token: validToken })
                .send();

            expect(res.status).to.equal(400);
        });

        it("should return 401 without token", async () => {
            const res = await request(server)
                .get("/v1/currencies/USD")
                .send();

            expect(res.status).to.equal(400);
        });

        it("should return 403 with invalid token", async () => {
            const res = await request(server)
                .get("/v1/currencies/USD")
                .query({ token: "invalid-token" })
                .send();

            expect(res.status).to.equal(403);
        });
    });
});
