import { expect } from "chai";
import { getSupportedCurrencies, getCurrenciesByType, isCurrencySupported } from "../../skywind/services/currencies";
import { logging } from "@skywind-group/sw-utils";

logging.setUpOutput({
    type: "console",
    logLevel: "info"
});

describe("Currencies Service", () => {
    describe("getSupportedCurrencies", () => {
        it("should return a list of supported currencies", async () => {
            const result = await getSupportedCurrencies();
            
            expect(result).to.have.property("total");
            expect(result).to.have.property("currencies");
            expect(result.total).to.be.a("number");
            expect(result.currencies).to.be.an("array");
            expect(result.total).to.equal(result.currencies.length);
            expect(result.total).to.be.greaterThan(0);
        });

        it("should include base currencies from config", async () => {
            const result = await getSupportedCurrencies();
            
            // Check for common base currencies
            const baseCurrencies = result.currencies.filter(c => c.type === "base");
            expect(baseCurrencies.length).to.be.greaterThan(0);
            
            // Should include EUR, USD, etc.
            const currencyCodes = baseCurrencies.map(c => c.code);
            expect(currencyCodes).to.include.members(["EUR", "USD"]);
        });

        it("should include standard currencies from default rates", async () => {
            const result = await getSupportedCurrencies();
            
            const standardCurrencies = result.currencies.filter(c => c.type === "standard");
            expect(standardCurrencies.length).to.be.greaterThan(0);
            
            // Should include common currencies like GBP, JPY, etc.
            const currencyCodes = standardCurrencies.map(c => c.code);
            expect(currencyCodes).to.include.members(["GBP", "JPY", "CAD"]);
        });

        it("should include artificial currencies", async () => {
            const result = await getSupportedCurrencies();
            
            const artificialCurrencies = result.currencies.filter(c => c.type === "artificial");
            expect(artificialCurrencies.length).to.be.greaterThan(0);
            
            // Artificial currencies should have specific properties
            artificialCurrencies.forEach(currency => {
                expect(currency.artificial).to.be.true;
                expect(currency).to.have.property("originCurrency");
                expect(currency.originCurrency).to.have.property("currency");
                expect(currency.originCurrency).to.have.property("multiplier");
            });
        });

        it("should return currencies sorted by code", async () => {
            const result = await getSupportedCurrencies();
            
            const codes = result.currencies.map(c => c.code);
            const sortedCodes = [...codes].sort();
            expect(codes).to.deep.equal(sortedCodes);
        });

        it("should not have duplicate currency codes", async () => {
            const result = await getSupportedCurrencies();
            
            const codes = result.currencies.map(c => c.code);
            const uniqueCodes = [...new Set(codes)];
            expect(codes.length).to.equal(uniqueCodes.length);
        });

        it("should have valid currency structure", async () => {
            const result = await getSupportedCurrencies();
            
            result.currencies.forEach(currency => {
                expect(currency).to.have.property("code");
                expect(currency).to.have.property("type");
                expect(currency).to.have.property("artificial");
                expect(currency.code).to.be.a("string");
                expect(currency.type).to.be.oneOf(["base", "standard", "artificial"]);
                expect(currency.artificial).to.be.a("boolean");
                
                if (currency.type === "artificial") {
                    expect(currency.artificial).to.be.true;
                } else {
                    expect(currency.artificial).to.be.false;
                }
            });
        });
    });

    describe("getCurrenciesByType", () => {
        it("should filter currencies by base type", async () => {
            const result = await getCurrenciesByType("base");
            
            expect(result.currencies).to.be.an("array");
            result.currencies.forEach(currency => {
                expect(currency.type).to.equal("base");
                expect(currency.artificial).to.be.false;
            });
        });

        it("should filter currencies by standard type", async () => {
            const result = await getCurrenciesByType("standard");
            
            expect(result.currencies).to.be.an("array");
            result.currencies.forEach(currency => {
                expect(currency.type).to.equal("standard");
                expect(currency.artificial).to.be.false;
            });
        });

        it("should filter currencies by artificial type", async () => {
            const result = await getCurrenciesByType("artificial");
            
            expect(result.currencies).to.be.an("array");
            result.currencies.forEach(currency => {
                expect(currency.type).to.equal("artificial");
                expect(currency.artificial).to.be.true;
                expect(currency).to.have.property("originCurrency");
            });
        });

        it("should return correct total count for filtered results", async () => {
            const baseResult = await getCurrenciesByType("base");
            const standardResult = await getCurrenciesByType("standard");
            const artificialResult = await getCurrenciesByType("artificial");
            const allResult = await getSupportedCurrencies();
            
            expect(baseResult.total).to.equal(baseResult.currencies.length);
            expect(standardResult.total).to.equal(standardResult.currencies.length);
            expect(artificialResult.total).to.equal(artificialResult.currencies.length);
            
            const totalFiltered = baseResult.total + standardResult.total + artificialResult.total;
            expect(totalFiltered).to.equal(allResult.total);
        });
    });

    describe("isCurrencySupported", () => {
        it("should return true for supported currencies", async () => {
            const isUSDSupported = await isCurrencySupported("USD");
            const isEURSupported = await isCurrencySupported("EUR");
            
            expect(isUSDSupported).to.be.true;
            expect(isEURSupported).to.be.true;
        });

        it("should return false for unsupported currencies", async () => {
            const isZZZSupported = await isCurrencySupported("ZZZ");
            const isInvalidSupported = await isCurrencySupported("INVALID");

            expect(isZZZSupported).to.be.false;
            expect(isInvalidSupported).to.be.false;
        });

        it("should be case sensitive (currencies are stored in uppercase)", async () => {
            const isUpperCaseSupported = await isCurrencySupported("USD");
            const isLowerCaseSupported = await isCurrencySupported("usd");

            expect(isUpperCaseSupported).to.be.true;
            // The service should handle case conversion internally
            expect(isLowerCaseSupported).to.be.false;
        });

        it("should handle artificial currencies", async () => {
            const artificialCurrencies = await getCurrenciesByType("artificial");
            
            if (artificialCurrencies.currencies.length > 0) {
                const firstArtificial = artificialCurrencies.currencies[0];
                const isSupported = await isCurrencySupported(firstArtificial.code);
                expect(isSupported).to.be.true;
            }
        });
    });
});
