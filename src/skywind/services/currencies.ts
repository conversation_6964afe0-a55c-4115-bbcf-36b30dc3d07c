import { Currencies } from "@skywind-group/sw-currency-exchange";
import config from "../config";
import logger from "../logger";

const log = logger();

const defaultCurrencyRates = require("../providers/default_currency_rates.json");

export interface CurrencyInfo {
    code: string;
    name?: string;
    type: "base" | "standard" | "artificial";
    artificial?: boolean;
    disableGGR?: boolean;
    provider?: string;
    originCurrency?: {
        currency: string;
        multiplier: number;
    };
}

export interface CurrenciesResponse {
    total: number;
    currencies: CurrencyInfo[];
}

/**
 * Get comprehensive list of all supported currencies
 * Combines base currencies, standard currencies from default rates, and artificial currencies
 */
export async function getSupportedCurrencies(): Promise<CurrenciesResponse> {
    try {
        const currencyMap = new Map<string, CurrencyInfo>();

        // Add base currencies from config
        for (const baseCurrency of config.baseCurrencies) {
            currencyMap.set(baseCurrency, {
                code: baseCurrency,
                type: "base",
                artificial: false
            });
        }

        // Add standard currencies from default rates
        for (const currencyCode of Object.keys(defaultCurrencyRates.rates)) {
            if (!currencyMap.has(currencyCode)) {
                currencyMap.set(currencyCode, {
                    code: currencyCode,
                    type: "standard",
                    artificial: false
                });
            }
        }

        // Add artificial currencies from the sw-currency-exchange package
        const artificialCurrencies = Currencies.artificialValues();
        for (const artificialCurrency of artificialCurrencies) {
            const currencyInfo: CurrencyInfo = {
                code: artificialCurrency.code,
                type: "artificial",
                artificial: true,
                disableGGR: artificialCurrency.disableGGR || false,
                originCurrency: {
                    currency: artificialCurrency.originCurrency.currency,
                    multiplier: artificialCurrency.originCurrency.multiplier
                }
            };

            currencyMap.set(artificialCurrency.code, currencyInfo);
        }

        // Add regular currencies from the sw-currency-exchange package
        const regularCurrencies = Currencies.values();
        for (const currency of regularCurrencies) {
            if (!currencyMap.has(currency.code)) {
                const currencyInfo: CurrencyInfo = {
                    code: currency.code,
                    type: "standard",
                    artificial: false,
                    provider: currency.provider
                };

                currencyMap.set(currency.code, currencyInfo);
            }
        }

        // Convert map to sorted array
        const currencies = Array.from(currencyMap.values()).sort((a, b) => a.code.localeCompare(b.code));

        log.info(`Retrieved ${currencies.length} supported currencies`);

        return {
            total: currencies.length,
            currencies
        };
    } catch (error) {
        log.error(error, "Error retrieving supported currencies");
        throw error;
    }
}

/**
 * Get currencies filtered by type
 */
export async function getCurrenciesByType(type: "base" | "standard" | "artificial"): Promise<CurrenciesResponse> {
    const allCurrencies = await getSupportedCurrencies();
    const filteredCurrencies = allCurrencies.currencies.filter(currency => currency.type === type);

    return {
        total: filteredCurrencies.length,
        currencies: filteredCurrencies
    };
}

/**
 * Check if a currency code is supported
 */
export async function isCurrencySupported(currencyCode: string): Promise<boolean> {
    const allCurrencies = await getSupportedCurrencies();
    return allCurrencies.currencies.some(currency => currency.code === currencyCode);
}
