import type { CurrencyDatasource, ProviderRates} from "../providers/provider";
import { Provider } from "../providers/provider";
import { getTimestamp } from "../providers/utils";
import config from "../config";

export class OXRCurrencyDatasource implements CurrencyDatasource {

    public async load(date: Date, baseCurrencies: string[]): Promise<ProviderRates> {
        const requests = baseCurrencies.map((baseCurrency) => this.requestRates(date, baseCurrency));
        const responses = await Promise.all(requests);
        const result: ProviderRates = {
            provider: Provider.OXR,
            ts: date,
            bidRates: {},
            askRates: {}
        };
        for (const data of responses) {
            result.bidRates[data["base"]] = result.askRates[data["base"]] = data["rates"];
        }
        return result;
    }

    private async requestRates(date: Date, baseCurrency: string): Promise<any> {
        const url = new URL(`https://openexchangerates.org/api/historical/${getTimestamp(date)}.json`);
        url.searchParams.set("base", baseCurrency);
        url.searchParams.set("show_alternative", "true");

        const res = await fetch(url, {
            headers: {
                "Authorization": `Token ${config.oxr.appKey}`
            }
        });
        if (!res.ok) {
            const text = await res.text();
            throw new Error(`OXR internal error: ${text}, status=${res.status}`);
        }
        return res.json();
    }
}
