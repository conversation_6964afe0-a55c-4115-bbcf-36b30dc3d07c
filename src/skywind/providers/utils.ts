import { ExchangeRateType } from "@skywind-group/sw-currency-exchange";
import type { ProviderExchangeRate } from "../entities/exchangeRates";
import type { ProviderRates } from "./provider";

const DAY = 24 * 60 * 60 * 1000;

export function getTimestamp(date: Date): string {
    return date instanceof Date ? date.toISOString().substr(0, 10) : date;
}

export function getToday(): Date {
    const date = new Date();
    date.setUTCHours(0, 0, 0, 0);
    return date;
}

export function getPrevDay(date = new Date(), days = 1): Date {
    return adjustDate(date, -days);
}

export function getNextDay(date = new Date(), days = 1): Date {
    return adjustDate(date, days);
}

function adjustDate(date: Date, days: number): Date {
    const nextDay = new Date(date);
    nextDay.setUTCDate(nextDay.getUTCDate() + days);
    nextDay.setUTCHours(0, 0, 0, 0);
    return nextDay;
}

export function getDaysCount(startDate: Date, endDate: Date) {
    return Math.floor((endDate.getTime() - startDate.getTime()) / DAY);
}

export function mapProviderRates({ askRates, bidRates, provider, ts }: ProviderRates): ProviderExchangeRate[] {
    const result: ProviderExchangeRate[] = [];
    for (const fromCurrency of Object.keys(bidRates)) {
        for (const toCurrency of Object.keys(bidRates[fromCurrency])) {
            result.push({
                from: fromCurrency,
                to: toCurrency,
                rate: bidRates[fromCurrency][toCurrency],
                providerDate: ts,
                provider,
                type: ExchangeRateType.BID
            });
        }
    }
    for (const fromCurrency of Object.keys(askRates)) {
        for (const toCurrency of Object.keys(askRates[fromCurrency])) {
            result.push({
                from: fromCurrency,
                to: toCurrency,
                rate: askRates[fromCurrency][toCurrency],
                providerDate: ts,
                provider,
                type: ExchangeRateType.ASK
            });
        }
    }
    return result;
}

export function getRateKey(rate: ProviderExchangeRate) {
    return `${rate.from}-${rate.to}-${rate.type}`;
}
