import { verifyToken } from "../token";
import { ValidationError } from "../errors";
import type { FastifyInstanceType } from "../fastify";
import * as CurrenciesService from "../services/currencies";
import logger from "../logger";

const log = logger();

export default function (router: FastifyInstanceType, _options, done) {
    router.get("/currencies",
        {
            schema: {
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" },
                        type: { 
                            type: "string", 
                            enum: ["base", "standard", "artificial"],
                            description: "Filter currencies by type"
                        },
                        includeMetadata: { 
                            type: "boolean", 
                            default: true,
                            description: "Include additional metadata for currencies"
                        }
                    },
                    required: ["token"]
                },
                response: {
                    200: {
                        type: "object",
                        properties: {
                            total: { type: "number" },
                            currencies: {
                                type: "array",
                                items: {
                                    type: "object",
                                    properties: {
                                        code: { type: "string" },
                                        name: { type: "string" },
                                        type: { 
                                            type: "string", 
                                            enum: ["base", "standard", "artificial"] 
                                        },
                                        artificial: { type: "boolean" },
                                        disableGGR: { type: "boolean" },
                                        provider: { type: "string" },
                                        originCurrency: {
                                            type: "object",
                                            properties: {
                                                currency: { type: "string" },
                                                multiplier: { type: "number" }
                                            }
                                        }
                                    },
                                    required: ["code", "type", "artificial"]
                                }
                            }
                        },
                        required: ["total", "currencies"]
                    }
                }
            }
        },
        async (req, res) => {
            try {
                await verifyToken(req.query.token);
                
                log.info({ 
                    type: req.query.type, 
                    includeMetadata: req.query.includeMetadata 
                }, "Fetching supported currencies");

                let result;
                if (req.query.type) {
                    result = await CurrenciesService.getCurrenciesByType(req.query.type);
                } else {
                    result = await CurrenciesService.getSupportedCurrencies();
                }

                // If metadata is not requested, simplify the response
                if (req.query.includeMetadata === false) {
                    result.currencies = result.currencies.map(currency => ({
                        code: currency.code,
                        type: currency.type,
                        artificial: currency.artificial
                    }));
                }

                log.info({ 
                    total: result.total, 
                    type: req.query.type || "all" 
                }, "Successfully retrieved currencies");

                return res.send(result);
            } catch (error) {
                log.error(error, "Error in currencies endpoint");
                throw error;
            }
        });

    router.get("/currencies/:code",
        {
            schema: {
                params: {
                    type: "object",
                    properties: {
                        code: {
                            type: "string",
                            pattern: "^[A-Za-z]{3,4}$",
                            description: "Currency code (3-4 letters)"
                        }
                    },
                    required: ["code"]
                },
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" }
                    },
                    required: ["token"]
                },
                response: {
                    200: {
                        type: "object",
                        properties: {
                            supported: { type: "boolean" },
                            currency: {
                                type: "object",
                                properties: {
                                    code: { type: "string" },
                                    name: { type: "string" },
                                    type: { 
                                        type: "string", 
                                        enum: ["base", "standard", "artificial"] 
                                    },
                                    artificial: { type: "boolean" },
                                    disableGGR: { type: "boolean" },
                                    provider: { type: "string" },
                                    originCurrency: {
                                        type: "object",
                                        properties: {
                                            currency: { type: "string" },
                                            multiplier: { type: "number" }
                                        }
                                    }
                                }
                            }
                        },
                        required: ["supported"]
                    },
                    404: {
                        type: "object",
                        properties: {
                            supported: { type: "boolean" },
                            message: { type: "string" }
                        },
                        required: ["supported", "message"]
                    }
                }
            }
        },
        async (req, res) => {
            try {
                await verifyToken(req.query.token);
                
                const currencyCode = req.params.code.toUpperCase();
                
                log.info({ currencyCode }, "Checking currency support");

                const isSupported = await CurrenciesService.isCurrencySupported(currencyCode);
                
                if (!isSupported) {
                    return res.status(404).send({
                        supported: false,
                        message: `Currency ${currencyCode} is not supported`
                    });
                }

                // Get the currency details
                const allCurrencies = await CurrenciesService.getSupportedCurrencies();
                const currency = allCurrencies.currencies.find(c => c.code === currencyCode);

                log.info({ currencyCode, supported: true }, "Currency support check completed");

                return res.send({
                    supported: true,
                    currency: currency as any
                });
            } catch (error) {
                log.error(error, "Error in currency check endpoint");
                throw error;
            }
        });

    done();
}
